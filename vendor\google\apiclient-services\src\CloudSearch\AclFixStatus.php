<?php
/*
 * Copyright 2014 Google Inc.
 *
 * Licensed under the Apache License, Version 2.0 (the "License"); you may not
 * use this file except in compliance with the License. You may obtain a copy of
 * the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
 * WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
 * License for the specific language governing permissions and limitations under
 * the License.
 */

namespace Google\Service\CloudSearch;

class AclFixStatus extends \Google\Collection
{
  protected $collection_key = 'outOfDomainWarningEmailAddress';
  /**
   * @var string
   */
  public $fixability;
  /**
   * @var string[]
   */
  public $fixableEmailAddress;
  /**
   * @var string[]
   */
  public $outOfDomainWarningEmailAddress;

  /**
   * @param string
   */
  public function setFixability($fixability)
  {
    $this->fixability = $fixability;
  }
  /**
   * @return string
   */
  public function getFixability()
  {
    return $this->fixability;
  }
  /**
   * @param string[]
   */
  public function setFixableEmailAddress($fixableEmailAddress)
  {
    $this->fixableEmailAddress = $fixableEmailAddress;
  }
  /**
   * @return string[]
   */
  public function getFixableEmailAddress()
  {
    return $this->fixableEmailAddress;
  }
  /**
   * @param string[]
   */
  public function setOutOfDomainWarningEmailAddress($outOfDomainWarningEmailAddress)
  {
    $this->outOfDomainWarningEmailAddress = $outOfDomainWarningEmailAddress;
  }
  /**
   * @return string[]
   */
  public function getOutOfDomainWarningEmailAddress()
  {
    return $this->outOfDomainWarningEmailAddress;
  }
}

// Adding a class alias for backwards compatibility with the previous class name.
class_alias(AclFixStatus::class, 'Google_Service_CloudSearch_AclFixStatus');
