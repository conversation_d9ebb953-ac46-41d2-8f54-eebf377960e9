<?php
/*
 * Copyright 2014 Google Inc.
 *
 * Licensed under the Apache License, Version 2.0 (the "License"); you may not
 * use this file except in compliance with the License. You may obtain a copy of
 * the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
 * WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
 * License for the specific language governing permissions and limitations under
 * the License.
 */

namespace Google\Service\CloudSearch;

class AppsDynamiteSharedChatItem extends \Google\Collection
{
  protected $collection_key = 'activityInfo';
  protected $activityInfoType = AppsDynamiteSharedChatItemActivityInfo::class;
  protected $activityInfoDataType = 'array';
  protected $groupInfoType = AppsDynamiteSharedChatItemGroupInfo::class;
  protected $groupInfoDataType = '';
  protected $messageInfoType = AppsDynamiteSharedMessageInfo::class;
  protected $messageInfoDataType = '';

  /**
   * @param AppsDynamiteSharedChatItemActivityInfo[]
   */
  public function setActivityInfo($activityInfo)
  {
    $this->activityInfo = $activityInfo;
  }
  /**
   * @return AppsDynamiteSharedChatItemActivityInfo[]
   */
  public function getActivityInfo()
  {
    return $this->activityInfo;
  }
  /**
   * @param AppsDynamiteSharedChatItemGroupInfo
   */
  public function setGroupInfo(AppsDynamiteSharedChatItemGroupInfo $groupInfo)
  {
    $this->groupInfo = $groupInfo;
  }
  /**
   * @return AppsDynamiteSharedChatItemGroupInfo
   */
  public function getGroupInfo()
  {
    return $this->groupInfo;
  }
  /**
   * @param AppsDynamiteSharedMessageInfo
   */
  public function setMessageInfo(AppsDynamiteSharedMessageInfo $messageInfo)
  {
    $this->messageInfo = $messageInfo;
  }
  /**
   * @return AppsDynamiteSharedMessageInfo
   */
  public function getMessageInfo()
  {
    return $this->messageInfo;
  }
}

// Adding a class alias for backwards compatibility with the previous class name.
class_alias(AppsDynamiteSharedChatItem::class, 'Google_Service_CloudSearch_AppsDynamiteSharedChatItem');
