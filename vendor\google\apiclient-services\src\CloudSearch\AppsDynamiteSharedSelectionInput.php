<?php
/*
 * Copyright 2014 Google Inc.
 *
 * Licensed under the Apache License, Version 2.0 (the "License"); you may not
 * use this file except in compliance with the License. You may obtain a copy of
 * the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
 * WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
 * License for the specific language governing permissions and limitations under
 * the License.
 */

namespace Google\Service\CloudSearch;

class AppsDynamiteSharedSelectionInput extends \Google\Collection
{
  protected $collection_key = 'items';
  protected $itemsType = AppsDynamiteSharedSelectionInputSelectionItem::class;
  protected $itemsDataType = 'array';
  public $items;
  /**
   * @var string
   */
  public $label;
  /**
   * @var string
   */
  public $name;
  protected $onChangeActionType = AppsDynamiteSharedAction::class;
  protected $onChangeActionDataType = '';
  public $onChangeAction;
  /**
   * @var string
   */
  public $type;

  /**
   * @param AppsDynamiteSharedSelectionInputSelectionItem[]
   */
  public function setItems($items)
  {
    $this->items = $items;
  }
  /**
   * @return AppsDynamiteSharedSelectionInputSelectionItem[]
   */
  public function getItems()
  {
    return $this->items;
  }
  /**
   * @param string
   */
  public function setLabel($label)
  {
    $this->label = $label;
  }
  /**
   * @return string
   */
  public function getLabel()
  {
    return $this->label;
  }
  /**
   * @param string
   */
  public function setName($name)
  {
    $this->name = $name;
  }
  /**
   * @return string
   */
  public function getName()
  {
    return $this->name;
  }
  /**
   * @param AppsDynamiteSharedAction
   */
  public function setOnChangeAction(AppsDynamiteSharedAction $onChangeAction)
  {
    $this->onChangeAction = $onChangeAction;
  }
  /**
   * @return AppsDynamiteSharedAction
   */
  public function getOnChangeAction()
  {
    return $this->onChangeAction;
  }
  /**
   * @param string
   */
  public function setType($type)
  {
    $this->type = $type;
  }
  /**
   * @return string
   */
  public function getType()
  {
    return $this->type;
  }
}

// Adding a class alias for backwards compatibility with the previous class name.
class_alias(AppsDynamiteSharedSelectionInput::class, 'Google_Service_CloudSearch_AppsDynamiteSharedSelectionInput');
