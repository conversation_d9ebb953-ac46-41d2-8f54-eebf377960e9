<?php
/*
 * Copyright 2014 Google Inc.
 *
 * Licensed under the Apache License, Version 2.0 (the "License"); you may not
 * use this file except in compliance with the License. You may obtain a copy of
 * the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
 * WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
 * License for the specific language governing permissions and limitations under
 * the License.
 */

namespace Google\Service\CloudSearch;

class AclFixRequest extends \Google\Collection
{
  protected $collection_key = 'recipientEmails';
  /**
   * @var string[]
   */
  public $recipientEmails;
  /**
   * @var string
   */
  public $role;
  /**
   * @var bool
   */
  public $shouldFix;

  /**
   * @param string[]
   */
  public function setRecipientEmails($recipientEmails)
  {
    $this->recipientEmails = $recipientEmails;
  }
  /**
   * @return string[]
   */
  public function getRecipientEmails()
  {
    return $this->recipientEmails;
  }
  /**
   * @param string
   */
  public function setRole($role)
  {
    $this->role = $role;
  }
  /**
   * @return string
   */
  public function getRole()
  {
    return $this->role;
  }
  /**
   * @param bool
   */
  public function setShouldFix($shouldFix)
  {
    $this->shouldFix = $shouldFix;
  }
  /**
   * @return bool
   */
  public function getShouldFix()
  {
    return $this->shouldFix;
  }
}

// Adding a class alias for backwards compatibility with the previous class name.
class_alias(AclFixRequest::class, 'Google_Service_CloudSearch_AclFixRequest');
