<?php
/*
 * Copyright 2014 Google Inc.
 *
 * Licensed under the Apache License, Version 2.0 (the "License"); you may not
 * use this file except in compliance with the License. You may obtain a copy of
 * the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
 * WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
 * License for the specific language governing permissions and limitations under
 * the License.
 */

namespace Google\Service\CloudSourceRepositories;

class ProjectConfig extends \Google\Model
{
  /**
   * @var bool
   */
  public $enablePrivateKeyCheck;
  /**
   * @var string
   */
  public $name;
  protected $pubsubConfigsType = PubsubConfig::class;
  protected $pubsubConfigsDataType = 'map';

  /**
   * @param bool
   */
  public function setEnablePrivateKeyCheck($enablePrivateKeyCheck)
  {
    $this->enablePrivateKeyCheck = $enablePrivateKeyCheck;
  }
  /**
   * @return bool
   */
  public function getEnablePrivateKeyCheck()
  {
    return $this->enablePrivateKeyCheck;
  }
  /**
   * @param string
   */
  public function setName($name)
  {
    $this->name = $name;
  }
  /**
   * @return string
   */
  public function getName()
  {
    return $this->name;
  }
  /**
   * @param PubsubConfig[]
   */
  public function setPubsubConfigs($pubsubConfigs)
  {
    $this->pubsubConfigs = $pubsubConfigs;
  }
  /**
   * @return PubsubConfig[]
   */
  public function getPubsubConfigs()
  {
    return $this->pubsubConfigs;
  }
}

// Adding a class alias for backwards compatibility with the previous class name.
class_alias(ProjectConfig::class, 'Google_Service_CloudSourceRepositories_ProjectConfig');
