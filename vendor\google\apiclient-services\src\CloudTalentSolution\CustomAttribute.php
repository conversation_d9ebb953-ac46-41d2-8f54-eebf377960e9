<?php
/*
 * Copyright 2014 Google Inc.
 *
 * Licensed under the Apache License, Version 2.0 (the "License"); you may not
 * use this file except in compliance with the License. You may obtain a copy of
 * the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
 * WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
 * License for the specific language governing permissions and limitations under
 * the License.
 */

namespace Google\Service\CloudTalentSolution;

class CustomAttribute extends \Google\Collection
{
  protected $collection_key = 'stringValues';
  /**
   * @var bool
   */
  public $filterable;
  /**
   * @var bool
   */
  public $keywordSearchable;
  /**
   * @var string[]
   */
  public $longValues;
  /**
   * @var string[]
   */
  public $stringValues;

  /**
   * @param bool
   */
  public function setFilterable($filterable)
  {
    $this->filterable = $filterable;
  }
  /**
   * @return bool
   */
  public function getFilterable()
  {
    return $this->filterable;
  }
  /**
   * @param bool
   */
  public function setKeywordSearchable($keywordSearchable)
  {
    $this->keywordSearchable = $keywordSearchable;
  }
  /**
   * @return bool
   */
  public function getKeywordSearchable()
  {
    return $this->keywordSearchable;
  }
  /**
   * @param string[]
   */
  public function setLongValues($longValues)
  {
    $this->longValues = $longValues;
  }
  /**
   * @return string[]
   */
  public function getLongValues()
  {
    return $this->longValues;
  }
  /**
   * @param string[]
   */
  public function setStringValues($stringValues)
  {
    $this->stringValues = $stringValues;
  }
  /**
   * @return string[]
   */
  public function getStringValues()
  {
    return $this->stringValues;
  }
}

// Adding a class alias for backwards compatibility with the previous class name.
class_alias(CustomAttribute::class, 'Google_Service_CloudTalentSolution_CustomAttribute');
