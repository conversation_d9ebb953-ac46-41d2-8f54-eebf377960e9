<?php
/*
 * Copyright 2014 Google Inc.
 *
 * Licensed under the Apache License, Version 2.0 (the "License"); you may not
 * use this file except in compliance with the License. You may obtain a copy of
 * the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
 * WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
 * License for the specific language governing permissions and limitations under
 * the License.
 */

namespace Google\Service\CloudSearch;

class AppsDynamiteSharedCalendarEventAnnotationDataCalendarEvent extends \Google\Model
{
  protected $endTimeType = AppsDynamiteSharedCalendarEventAnnotationDataCalendarEventTime::class;
  protected $endTimeDataType = '';
  /**
   * @var string
   */
  public $eventId;
  protected $startTimeType = AppsDynamiteSharedCalendarEventAnnotationDataCalendarEventTime::class;
  protected $startTimeDataType = '';
  /**
   * @var string
   */
  public $title;

  /**
   * @param AppsDynamiteSharedCalendarEventAnnotationDataCalendarEventTime
   */
  public function setEndTime(AppsDynamiteSharedCalendarEventAnnotationDataCalendarEventTime $endTime)
  {
    $this->endTime = $endTime;
  }
  /**
   * @return AppsDynamiteSharedCalendarEventAnnotationDataCalendarEventTime
   */
  public function getEndTime()
  {
    return $this->endTime;
  }
  /**
   * @param string
   */
  public function setEventId($eventId)
  {
    $this->eventId = $eventId;
  }
  /**
   * @return string
   */
  public function getEventId()
  {
    return $this->eventId;
  }
  /**
   * @param AppsDynamiteSharedCalendarEventAnnotationDataCalendarEventTime
   */
  public function setStartTime(AppsDynamiteSharedCalendarEventAnnotationDataCalendarEventTime $startTime)
  {
    $this->startTime = $startTime;
  }
  /**
   * @return AppsDynamiteSharedCalendarEventAnnotationDataCalendarEventTime
   */
  public function getStartTime()
  {
    return $this->startTime;
  }
  /**
   * @param string
   */
  public function setTitle($title)
  {
    $this->title = $title;
  }
  /**
   * @return string
   */
  public function getTitle()
  {
    return $this->title;
  }
}

// Adding a class alias for backwards compatibility with the previous class name.
class_alias(AppsDynamiteSharedCalendarEventAnnotationDataCalendarEvent::class, 'Google_Service_CloudSearch_AppsDynamiteSharedCalendarEventAnnotationDataCalendarEvent');
